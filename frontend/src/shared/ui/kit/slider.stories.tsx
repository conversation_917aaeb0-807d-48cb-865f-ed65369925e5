import type { Meta, StoryObj } from '@storybook/react-vite'

import { Slider } from './slider'
import { useState } from 'react'

const meta = {
    component: Slider,
} satisfies Meta<typeof Slider>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    render: (args) => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [value, setValue] = useState([1])
        const data = [
            { title: '1 период', details: 'Демо' },
            { title: '2 периода', details: 'Минимальный' },
            { title: '3 периода', details: 'Стандарт' },
            { title: '4 периода', details: 'Профессиональный' },
        ]

        return (
            <>
                <Slider
                    value={value}
                    onValueChange={setValue}
                    marks={data}
                    {...args}
                />
            </>
        )
    },
    args: {
        disabled: false,
    },
}
