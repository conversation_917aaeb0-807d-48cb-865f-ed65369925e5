import * as React from 'react'
import * as SliderPrimitive from '@radix-ui/react-slider'
import { cn } from '@/shared/lib/css'
import { useRef, useLayoutEffect, useState } from 'react'

type SliderProps = React.ComponentProps<typeof SliderPrimitive.Root> & {
    marks?: MarkProps[]
}

function Slider({ className, defaultValue, value, marks, min = 0, max, ...props }: SliderProps) {
    const _values = React.useMemo(
        () =>
            Array.isArray(value) ? value : Array.isArray(defaultValue) ? defaultValue : [min, max],
        [
            value,
            defaultValue,
            min,
            max,
        ],
    )
    const markRef = useRef<HTMLDivElement>(null)
    const [markHeight, setMarkHeight] = useState(0)

    // Measure mark height after render
    useLayoutEffect(() => {
        if (markRef.current && marks) {
            const height = markRef.current.getBoundingClientRect().height
            setMarkHeight(height)
        }
    }, [marks])

    if (marks && (max || min)) {
        throw new Error("Don't use max/min with marks, it calculated automatically")
    }

    // Calculate padding-top: 20px (top-5) + mark height
    const sliderPaddingTop = marks ? 20 + markHeight : 0

    return (
        <>
            <SliderPrimitive.Root
                className={cn('relative flex w-fulltouch-none select-none items-center', className)}
                style={{ paddingTop: sliderPaddingTop }}
                value={value}
                max={marks ? marks.length - 1 : max}
                min={min}
                {...props}
            >
                <SliderPrimitive.Track className="relative h-1 w-full grow overflow-hidden rounded-full bg-primary/20">
                    <SliderPrimitive.Range className="absolute h-full bg-primary" />
                </SliderPrimitive.Track>

                {marks && (
                    <>
                        <div className="absolute top-5 flex grow w-full items-center justify-between z-10 px-[12px]">
                            {marks.map((mark, index) => {
                                return (
                                    <div
                                        className="relative group"
                                        data-active={_values[0] === index}
                                        key={mark.title}
                                    >
                                        <div
                                            className={cn(
                                                'absolute flex flex-col -translate-x-1/2 w-[140px] text-center text-sm',
                                            )}
                                        >
                                            <Mark
                                                title={mark.title}
                                                details={mark.details}
                                            />
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                    </>
                )}

                {Array.from({ length: _values.length }, (_, index) => (
                    <SliderPrimitive.Thumb
                        data-slot="slider-thumb"
                        key={index}
                        className="border-primary cursor-pointer bg-background block size-7 border-3 shrink-0 rounded-full shadow-sm transition-[color,box-shadow] focus-visible:outline-hidden data-disabled:pointer-events-none data-disabled:border-primary/50"
                    />
                ))}
            </SliderPrimitive.Root>
            {/* Hidden Mark for height measurement */}
            {marks && marks.length > 0 && (
                <Mark
                    ref={markRef}
                    title={marks[0].title}
                    details={marks[0].details}
                    className="absolute invisible pointer-events-none"
                />
            )}
        </>
    )
}

interface MarkProps {
    title: string
    details: string
    className?: string
}

const Mark = ({
    ref,
    title,
    details,
    className,
}: MarkProps & { ref?: React.RefObject<HTMLDivElement | null> }) => {
    return (
        <div
            className={className}
            ref={ref}
        >
            <p className="text-foreground/50 group-data-[active=true]:text-foreground">{title}</p>
            <p className="text-muted-foreground/50 group-data-[active=true]:text-muted-foreground">
                {details}
            </p>
        </div>
    )
}

Mark.displayName = 'Mark'

export { Slider }
